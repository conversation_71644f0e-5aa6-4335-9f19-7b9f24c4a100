export interface SettlementOfferModel {
    gid?: number;
    loan_id: number;
    ratio: number;
    fixed_amount: number;
    is_queued_offer: boolean;
    expiration_date: string;
    loan_balance?: number;
    created_by_username?: string;
    employee_name?: string;
    employee_id?: number;
    recipient_email?: string;
}

export interface CreateSettlementOfferRequest {
    ratio: number;
    fixed_amount: number;
    expiration_date: string;
    recipient_email: string;
    balance_asof_creation: number;
} 