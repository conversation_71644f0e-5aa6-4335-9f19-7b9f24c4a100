'use client';
import './styles.scss';

import React, {useEffect, useMemo, useRef, useState} from 'react';
import {notFound, usePathname, useRouter} from 'next/navigation';
import {Typography} from '@mui/material';
import {ADMIN_ROUTES, useSecured} from '@/hooks/useSecured';
import {
    AccountingScreen,
    AccountsScreen,
    ManagerScreen,
    CollectionsScreen,
    ComplianceScreen,
    MonitorScreen,
    OnboardingScreen2,
    OperatorScreen,
    UnderwritingScreen,
    VerificationScreen,
    OriginationScreen,
} from '@/screens';
import {GlobalModalProvider, KasLoading} from '@/components';
import {capitalizeWords} from '@/utils/TextUtils';
import {HashHandlerProvider} from '@/hooks/useHashHandler';
import {ScrollToTop} from './components';
import {useWindowEventObserver} from '@/hooks/useWindowEventObserver';
import {DashboardScreenType} from '@/interfaces';

interface ProtectSecureViewProps {
    path?: DashboardScreenType;
}

export const ProtectSecuredView = ({path}: ProtectSecureViewProps) => {
    const router = useRouter();
    const pathname = usePathname();
    const {isAvailableRoute, routes, setActiveRoute} = useSecured();
    const containerRef = useRef<HTMLDivElement | null>(null);
    const [visibleScrollToTop, setVisibleScrollToTop] = useState(false);

    const renderView = useMemo(() => {
        document.title = path ? capitalizeWords(path) : 'Kashable';

        switch (path) {
            case DashboardScreenType.Accounting:
                return <AccountingScreen />;
            case DashboardScreenType.Underwriting:
                return <UnderwritingScreen />;
            case DashboardScreenType.Accounts:
                return <AccountsScreen />;
            case DashboardScreenType.Verification:
                return <VerificationScreen />;
            case DashboardScreenType.Compliance:
                return <ComplianceScreen />;
            case DashboardScreenType.Manager:
                return <ManagerScreen />;
            case DashboardScreenType.Onboarding2:
                return <OnboardingScreen2 />;
            case DashboardScreenType.Monitor:
                return <MonitorScreen />;
            case DashboardScreenType.Collections:
                return <CollectionsScreen />;
            case DashboardScreenType.Operator:
                return <OperatorScreen />;
            case DashboardScreenType.Origination:
                return <OriginationScreen />;
            default:
                return <Typography variant='h3'>Path: {path}</Typography>;
        }
    }, [path]);

    useWindowEventObserver('scroll', containerRef, (value: number) => {
        setVisibleScrollToTop(value <= 0);
    });

    useEffect(() => {
        if (!path) {
            if (routes.length) {
                setActiveRoute(routes[0]);
                router.replace(`${pathname}/${routes[0]}`);
            } else {
                router.replace('/forbidden');
            }
        } else {
            setActiveRoute(path);
            if (!ADMIN_ROUTES.includes(path)) {
                notFound();
            } else {
                if (!isAvailableRoute(path)) {
                    router.replace('/forbidden');
                }
            }
        }
    }, [path, routes]);

    return (
        <HashHandlerProvider>
            <GlobalModalProvider>
                <div ref={containerRef} className='kas-protected-secured-view'>
                    {path && isAvailableRoute(path) ? (
                        renderView
                    ) : (
                        <div className='kas-protected-secured-view__loading'>
                            <KasLoading size={100} />
                        </div>
                    )}
                </div>
                <ScrollToTop visible={visibleScrollToTop} />
            </GlobalModalProvider>
        </HashHandlerProvider>
    );
};
