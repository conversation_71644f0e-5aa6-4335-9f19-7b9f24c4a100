import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {ConsumersReportParams} from '@/app/api/secured/compliance/consumers/report/interfaces';

const getSkiptraceInfo = ({type, id}: ConsumersReportParams) => {
    const url = `/secured/compliance/report/${type}/${id}/skiptrace`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<any>>(url)
        .then((res) => mapSucceeded(res, ({data}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest, {params}: {params: Promise<ConsumersReportParams>}) {
    const requestParams = await params;
    const response = await getSkiptraceInfo(requestParams);

    return NextResponse.json(response, {status: response.code});
}
