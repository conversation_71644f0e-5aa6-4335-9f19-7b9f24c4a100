import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';

const setResendVerified = (type: 'email' | 'phone', id: string) => {
    const url = `/secured/uw/action/user/${id}/resend/verification/${type}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<boolean>>(url)
        .then((res) => mapSucceeded(res, ({data}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(
    request: NextRequest,
    {params}: {params: Promise<{id: string; type: 'email' | 'phone'}>},
) {
    const {id, type} = await params;
    const response = await setResendVerified(type, id);

    return NextResponse.json(response, {status: response.code});
}
