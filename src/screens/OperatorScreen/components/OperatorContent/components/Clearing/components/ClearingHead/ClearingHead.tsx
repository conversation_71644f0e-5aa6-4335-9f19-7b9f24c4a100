import React, {useEffect, useMemo} from 'react';
import {<PERSON><PERSON>, Checkbox, FormControlLabel, Grid2, Stack, Typography} from '@mui/material';
import {useClearing} from '../../useClearing';
import {useFormik} from 'formik';
import {ClearingHeadValues, validationSchema} from './schema';
import {useOperator} from '@/screens/OperatorScreen/useOperator';
import {OperatorItem} from '@/screens/OperatorScreen/interfaces';
import {KasAutocompleteField, KasDatePickerFormField, KasDotsMenu, KasDotsMenuItemProps} from '@/components';
import {TYPE_OPTIONS} from './data';
import dayjs from 'dayjs';
import {ClearingItemModal} from './../../interfaces';

export const ClearingHead = () => {
    const {activeMenu} = useOperator();
    const {dataState, loadData, setOpenModal} = useClearing();

    const onSubmit = async (values: ClearingHeadValues) => {
        const params = new URLSearchParams({
            future: String(values.future),
            date: dayjs(values.date).format('YYYYMMDD'),
            ...(values.type && {type: values.type}),
        }).toString();

        await loadData(params);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: dayjs().format('YYYYMMDD'),
            type: '',
            future: false,
        },
        onSubmit,
        validationSchema,
    });

    const menuItems: KasDotsMenuItemProps[] = useMemo(
        () => [
            {
                ContentComponent: <Typography variant='body1'>Batch Clear</Typography>,
                onClick: () =>
                    setOpenModal({
                        type: ClearingItemModal.Clear_All,
                        props: {
                            type: formik.values.type,
                            date: dayjs(formik.values.date).format('YYYYMMDD'),
                        },
                    }),
            },
        ],
        [formik.values],
    );

    useEffect(() => {
        if (activeMenu === OperatorItem.CLEARING && !dataState.data) {
            formik.handleSubmit();
        }
    }, [activeMenu]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={2.5}>
                    <Typography variant='subtitle1' py={1}>
                        Payment Clearing Queue
                    </Typography>
                </Grid2>
                <Grid2 size={2}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='date'
                        label='Date'
                        disableFuture
                        disabled={dataState.loading}
                    />
                </Grid2>
                <Grid2 size={2}>
                    <KasAutocompleteField
                        name='type'
                        formik={formik}
                        label='Type'
                        disabled={dataState.loading}
                        options={TYPE_OPTIONS}
                    />
                </Grid2>
                <Grid2 size={3}>
                    <FormControlLabel
                        disabled={dataState.loading}
                        label='Preview Future Clearings'
                        control={
                            <Checkbox
                                size='small'
                                name='future'
                                onChange={formik.handleChange}
                                checked={formik.values.future}
                            />
                        }
                    />
                </Grid2>
                <Grid2 size={2.5} ml='auto'>
                    <Stack flexDirection='row' columnGap={1}>
                        <Button
                            fullWidth
                            variant='contained'
                            size='small'
                            type='submit'
                            loading={dataState.loading}
                            disabled={dataState.loading}>
                            Search
                        </Button>
                        <KasDotsMenu
                            disabled={formik.values.future || dataState.loading}
                            menuItems={menuItems}
                        />
                    </Stack>
                </Grid2>
            </Grid2>
        </form>
    );
};
