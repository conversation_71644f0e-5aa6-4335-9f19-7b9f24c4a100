import React, {useState} from 'react';
import {useFormik} from 'formik';
import {Alert, Grid2} from '@mui/material';
import {KasModalFooter} from '@/components';
import {useSnackbar} from '@/hooks/useSnackbar';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useClearing} from '../../../../useClearing';
import {ClearingAllProps} from '@/screens/OperatorScreen/components/OperatorContent/components/Clearing/interfaces';

export const ClearAll = ({type, date}: ClearingAllProps) => {
    const {showMessage} = useSnackbar();
    const {setOpenModal, loadData} = useClearing();
    const [submitting, setSubmitting] = useState(false);

    const onSubmit = async () => {
        const url = `/api/secured/operator/clearing/all`;
        const payload = {ach_type: type, end_date: date};

        setSubmitting(true);

        const response = await apiRequest(url, {
            method: 'post',
            body: JSON.stringify(payload),
        });

        if (response.error) {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        } else {
            setOpenModal(null);
            const params = new URLSearchParams({
                date,
                type,
            }).toString();
            await loadData(params);
        }

        setSubmitting(false);
    };

    const formik = useFormik({
        initialValues: {},
        onSubmit,
    });

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2}>
                <Grid2 size={12}>
                    <Alert severity='warning'>
                        Are you sure you want to clear all ACH agreements until {date}?
                    </Alert>
                </Grid2>
                <Grid2 size={12}>
                    <KasModalFooter
                        disabled={submitting}
                        submitText='OK'
                        onCancel={() => setOpenModal(null)}
                    />
                </Grid2>
            </Grid2>
        </form>
    );
};
