import {OperatorPaymentModel} from '@/interfaces';

export enum ClearingItemModal {
    Set_Cleared = 'Mark Cleared',
    Reject_Payment = 'Reject Payment',
    Clear_All = 'Clear All',
}

export interface ClearingAllProps {
    type: string;
    date: string;
}

export type ClearingModalProps =
    | {
          type: ClearingItemModal.Set_Cleared;
          props: {data: OperatorPaymentModel};
      }
    | {
          type: ClearingItemModal.Reject_Payment;
          props: {data: OperatorPaymentModel};
      }
    | {
          type: ClearingItemModal.Clear_All;
          props: ClearingAllProps;
      };
