import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {
    TransactionAudits,
    RemoveTransactionForm,
    EditEmployeeTransaction,
    AddEmployeeTransaction,
} from './components';
import {useTransactionsEmployee} from './../../useTransactionsEmployee';
import {TransactionsEmployeeModalType} from './../../interfaces';

export const TransactionsEmployeeModal = () => {
    const {openModal, setOpenModal} = useTransactionsEmployee();

    const size = useMemo(() => {
        switch (openModal?.type) {
            case TransactionsEmployeeModalType.Delete_Transaction:
                return 'small';
            default:
                return 'medium';
        }
    }, [openModal?.type]);

    const renderModalContent = useMemo(() => {
        switch (openModal?.type) {
            case TransactionsEmployeeModalType.Add_Transaction:
                return <AddEmployeeTransaction />;
            case TransactionsEmployeeModalType.Edit_Transaction:
                return <EditEmployeeTransaction {...openModal.props} />;
            case TransactionsEmployeeModalType.Delete_Transaction:
                return <RemoveTransactionForm {...openModal.props} />;
            case TransactionsEmployeeModalType.Transaction_Audits:
                return <TransactionAudits {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal
            title={openModal?.type || 'Unknown Type'}
            size={size}
            open={!!openModal}
            onClose={() => setOpenModal(null)}>
            {renderModalContent}
        </KasModal>
    );
};
