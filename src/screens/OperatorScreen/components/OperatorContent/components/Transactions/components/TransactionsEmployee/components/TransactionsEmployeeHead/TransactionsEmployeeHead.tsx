import React from 'react';
import {Stack, Typography} from '@mui/material';
import {Refresh} from '@mui/icons-material';
import IconButton from '@mui/material/IconButton';
import {KasDotsMenuSingle, KasLoadingStatusIcon, KasSwitch, KasSwitchWhen} from '@/components';
import {useTransactionsEmployee} from './../../useTransactionsEmployee';
import {EmployeeTitle} from './../../../../components';
import {TransactionsEmployeeModalType} from './../../interfaces';

export const TransactionsEmployeeHead = () => {
    const {
        setOpenModal,
        employee,
        dataState: {loading, error},
        loadData,
    } = useTransactionsEmployee();

    return (
        <Stack flexDirection='row' alignItems='center' justifyContent='space-between' spacing={2}>
            <Typography variant='h3'>
                <EmployeeTitle data={employee.label} />
            </Typography>
            <Stack flexDirection='row' columnGap={1}>
                <KasSwitch>
                    <KasSwitchWhen condition={loading || !!error}>
                        <KasLoadingStatusIcon loading={loading} loadingError={error} />
                    </KasSwitchWhen>
                    <KasSwitchWhen condition={!loading}>
                        <IconButton title={'Refresh'} onClick={loadData}>
                            <Refresh />
                        </IconButton>
                    </KasSwitchWhen>
                </KasSwitch>
                <KasDotsMenuSingle
                    title='Add Transaction'
                    onClick={() =>
                        setOpenModal({
                            type: TransactionsEmployeeModalType.Add_Transaction,
                        })
                    }
                />
            </Stack>
        </Stack>
    );
};
