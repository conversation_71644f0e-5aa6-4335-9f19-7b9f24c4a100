import {OperatorTransactionModel} from '@/interfaces';

export enum TransactionsEmployeeModalType {
    Add_Transaction = 'Add Transaction',
    Edit_Transaction = 'Edit Transaction',
    Delete_Transaction = 'Delete Transaction',
    Transaction_Audits = 'Transaction Audits',
}

export type TransactionsEmployeeModalProps =
    | {
          type: TransactionsEmployeeModalType.Add_Transaction;
      }
    | {
          type: TransactionsEmployeeModalType.Edit_Transaction;
          props: {data: OperatorTransactionModel};
      }
    | {type: TransactionsEmployeeModalType.Delete_Transaction; props: {data: OperatorTransactionModel}}
    | {type: TransactionsEmployeeModalType.Transaction_Audits; props: {gid: number}};
