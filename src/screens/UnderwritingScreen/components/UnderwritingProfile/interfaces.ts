export enum ProfileActionModalFormEntityClass {
    Employee = 'Employee',
    User = 'Users',
}

export interface ProfileActionModalFormProps {
    entityId: number;
    entityClass: ProfileActionModalFormEntityClass;
}

export enum UnderwritingProfileAction {
    ADD_COMMENT = 'ADD_COMMENT',
    DIRECT_DEPOSIT_REPORT = 'DIRECT_DEPOSIT_REPORT',
    REQUEST_LETTER = 'REQUEST_LETTER',
    SET_TERMINATED = 'SET_TERMINATED',
    SET_DECEASED = 'SET_DECEASED',
    UNFLAG_DECEASED = 'UNFLAG_DECEASED',
    PAID_LEAVE = 'PAID_LEAVE',
    UNPAID_LEAVE = 'UNPAID_LEAVE',
    UPLOAD_ACH = 'UPLOAD_ACH',
    PAYROLL_DEDUCTION_REVOKE = 'PAYROLL_DEDUCTION_REVOKE',
    PAYROLL_DEDUCTION_AUTHORIZE = 'PAYROLL_DEDUCTION_AUTHORIZE',
    ADD_LOAN_HARDSHIP = 'ADD_LOAN_HARDSHIP',
    REMOVE_LOAN_HARDSHIP = 'REMOVE_LOAN_HARDSHIP',
    UNFLAG_INCOME = 'UNFLAG_INCOME',
    CLOSE_BANKRUPTCY = 'CLOSE_BANKRUPTCY',
    ADD_REFERRAL = 'ADD_REFERRAL',
    FLAGGING_MODAL = 'FLAGGING_MODAL',
    FLAGGING_SCRA_MODAL = 'FLAGGING_SCRA_MODAL',
    UNFLAG_REVIEW = 'UNFLAG_REVIEW',
    VOID_LOAN = 'VOID_LOAN',
    DECLINE_APPLICATION = 'DECLINE_APPLICATION',
    DISBURSEMENT_REDISBURSE_LOAN = 'DISBURSEMENT_REDISBURSE_LOAN',
    CHARGEOFF_LOAN = 'CHARGEOFF_LOAN',
    SETTLE_LOAN = 'SETTLE_LOAN',
    CANCEL_ACH_PAYMENT = 'CANCEL_ACH_PAYMENT',
    CLEAR_EMPLOYMENT = 'CLEAR_EMPLOYMENT',
    MANUAL_DEBIT_CARD_PAYMENT = 'MANUAL_DEBIT_CARD_PAYMENT',
    UNENROLL_CREDIT_MONITORING = 'UNENROLL_CREDIT_MONITORING',
    RUN_FULFILLMENT = 'RUN_FULFILLMENT',
    RUN_FULFILLMENT_BF = 'RUN_FULFILLMENT_BF',
    FLAG_REVIEW = 'FLAG_REVIEW',
    LETTER_PREVIEW = 'LETTER_PREVIEW',
    FLAG_FOR_BANKRUPTCY = 'FLAG_FOR_BANKRUPTCY',
    CLEAR_BANKRUPTCY = 'CLEAR_BANKRUPTCY',
    IDENTITY_VERIFIED = 'IDENTITY_VERIFIED',
    RESTRICT_CONTACT = 'RESTRICT_CONTACT',
    PROCEED_EDIT_EMPLOYEE_PROFILE = 'PROCEED_EDIT_EMPLOYEE_PROFILE',
    EDIT_EMPLOYEE_PROFILE = 'EDIT_EMPLOYEE_PROFILE',
    SEND_CTA_EMAIL = 'SEND_CTA_EMAIL',
    PUSH_USER_INTO_QUEUE = 'PUSH_USER_INTO_QUEUE',
    UPLOAD_DOCUMENT = 'UPLOAD_DOCUMENT',
    UNLINK_USER = 'UNLINK_USER',
    VERIFICATION_STATUS = 'VERIFICATION_STATUS',
    RESET_MFA = 'RESET_MFA',
    PRIVACY_OPT_OUT_EMAIL = 'PRIVACY_OPT_OUT_EMAIL',
    RESEND_REFERRAL_EMAIL = 'RESEND_REFERRAL_EMAIL',
    SET_PRIMARY_DIRECT_DEPOSIT = 'SET_PRIMARY_DIRECT_DEPOSIT',
}

export interface UnderwritingProfileVerificationStatusProps {
    gid: number;
    type: 'email' | 'phone';
    verifiedDate: string | null;
}

export type UnderwritingProfileActionModalProps =
    | {
          type: Exclude<
              UnderwritingProfileAction,
              | UnderwritingProfileAction.VERIFICATION_STATUS
              | UnderwritingProfileAction.RESEND_REFERRAL_EMAIL
              | UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT
          >;
      }
    | {
          type: UnderwritingProfileAction.VERIFICATION_STATUS;
          props: UnderwritingProfileVerificationStatusProps;
      }
    | {
          type: UnderwritingProfileAction.RESEND_REFERRAL_EMAIL;
          props: {
              id: number;
              force?: boolean;
          };
      }
    | {
          type: UnderwritingProfileAction.SET_PRIMARY_DIRECT_DEPOSIT;
          props: {
              id: number;
          };
      };

export interface UnderwritingProfileLoanAlertsModel {
    bankruptcyAlert: boolean | null;
    scraAlert: boolean | null;
}
