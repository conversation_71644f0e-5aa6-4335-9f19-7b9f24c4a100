import {DataStateInterface} from '@/interfaces';
import {
    BankReportModel,
    BankruptcyReportModel,
    CreditReportModel,
    EmploymentReportModel,
    FraudReportModel,
    RiskReportModel,
    SkiptraceReportModel,
} from '@/interfaces';
import {PayrollElectionReportDTO} from '@/models';

export enum ConsumersReportItemType {
    Credit = 'Credit',
    Risk = 'Risk',
    Fraud = 'Fraud',
    Employment = 'Employment',
    Bankruptcy = 'Bankruptcy',
    Skiptrace = 'Skiptrace',
    Digital_Identity = 'Digital Identity',
    Bank = 'Bank',
    Payroll = 'Payroll',
}

export type ConsumersReportPath = Record<ConsumersReportItemType, string>;

export type ReportsStateType = {
    [ConsumersReportItemType.Credit]: DataStateInterface<CreditReportModel>;
} & {
    [ConsumersReportItemType.Risk]: DataStateInterface<RiskReportModel>;
} & {
    [ConsumersReportItemType.Fraud]: DataStateInterface<FraudReportModel>;
} & {
    [ConsumersReportItemType.Employment]: DataStateInterface<EmploymentReportModel>;
} & {
    [ConsumersReportItemType.Bankruptcy]: DataStateInterface<BankruptcyReportModel>;
} & {
    [ConsumersReportItemType.Skiptrace]: DataStateInterface<SkiptraceReportModel>;
} & {
    [ConsumersReportItemType.Digital_Identity]: DataStateInterface<unknown>;
} & {
    [ConsumersReportItemType.Bank]: DataStateInterface<BankReportModel>;
} & {
    [ConsumersReportItemType.Payroll]: DataStateInterface<PayrollElectionReportDTO[]>;
};
