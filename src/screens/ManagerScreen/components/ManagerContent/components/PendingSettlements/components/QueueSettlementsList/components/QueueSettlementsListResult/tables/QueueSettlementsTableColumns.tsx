import {CellContext, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {SettlementOfferModel} from '@/interfaces/settlement-offer.interface';
import React from 'react';
import {Chip} from '@mui/material';
import {QueueSettlementActionCell} from './../components/QueueSettlementActionCell/QueueSettlementActionCell';
import {AmountCell} from '@/components/table/cells';
import {KasUnderwritingSharedLink} from '@/components';
import {toPercentage, toCurrency} from '@/utils/FormatUtils';
import dayjs from 'dayjs';

const columnHelper = createColumnHelper<SettlementOfferModel>();

const _defaultInfoColumn = defaultInfoColumn<SettlementOfferModel>;

export const QueueSettlementsTableColumns = [
    {
        id: 'employeeInfo',
        header: 'Employee',
        enableSorting: false,
        cell: (props: CellContext<SettlementOfferModel, string>) => {
            const {employee_name, employee_id} = props.row.original;
            return (
                <>
                    {employee_name} {employee_id && (
                        <>[<KasUnderwritingSharedLink id={employee_id} />]</>
                    )}
                </>
            );
        },
    },
    _defaultInfoColumn('loan_id', 'Loan ID'),
    columnHelper.accessor('ratio', {
        id: 'ratio',
        header: 'Settlement Percent',
        cell: (props) => {
            const percentage = toPercentage(props.getValue());
            return <Chip size='small' label={percentage} variant='outlined' color='primary' />;
        },
    }),
    columnHelper.accessor('fixed_amount', {
        id: 'fixed_amount',
        header: 'Settlement Amount',
        cell: (props) => {
            return <AmountCell data={props.getValue()} />;
        },
    }),
    columnHelper.accessor('expiration_date', {
        id: 'expiration_date',
        header: 'Expiration Date',
        cell: (props) => {
            return dayjs(props.getValue()).format('YYYY-MM-DD');
        },
    }),
    _defaultInfoColumn('created_by_username', 'Created By'),
    columnHelper.accessor('gid', {
        id: 'action',
        header: 'Action',
        cell: (props) => {
            const queueId = props.getValue();
            return queueId && <QueueSettlementActionCell queueId={queueId} />;
        },
        enableSorting: false,
        meta: {
            notExport: true,
        },
    }),
]; 