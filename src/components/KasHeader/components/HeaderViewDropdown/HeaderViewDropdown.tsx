import './styles.scss';
import React, {ReactNode, useMemo} from 'react';
import MenuItem from '@mui/material/MenuItem';
import {ListItemIcon, SelectChangeEvent, ThemeProvider, Stack} from '@mui/material';
import {
    AttachMoney,
    SupervisorAccount,
    InsertChart,
    Whatshot,
    TableView,
    Work,
    Computer,
    SyncAlt,
    VerifiedUser,
    Monitor,
    Collections,
    MenuBook,
    Construction,
    Settings,
    AssignmentInd,
    NoteAlt,
} from '@mui/icons-material';
import {theme} from './theme';
import {useRouter} from 'next/navigation';
import {useSecured} from '@/hooks/useSecured';
import {KasSelect} from '@/components';
import {DashboardScreenType} from '@/interfaces';

interface ValueModel {
    text: string;
    value: string;
    Icon: ReactNode;
}

const VALUES: ValueModel[] = [
    {
        text: 'Accounting Dashboard',
        value: DashboardScreenType.Accounting,
        Icon: <AttachMoney fontSize='small' />,
    },
    {
        text: 'Admin Dashboard',
        value: DashboardScreenType.Admin,
        Icon: <SupervisorAccount fontSize='small' />,
    },
    {
        text: 'Manager Dashboard',
        value: DashboardScreenType.Manager,
        Icon: <AssignmentInd fontSize='small' />,
    },
    {
        text: 'Statistics',
        value: DashboardScreenType.Statistics,
        Icon: <InsertChart fontSize='small' />,
    },
    {
        text: 'Performance',
        value: DashboardScreenType.Performance,
        Icon: <Whatshot fontSize='small' />,
    },
    {
        text: 'Underwriting Dashboard',
        value: DashboardScreenType.Underwriting,
        Icon: <TableView fontSize='small' />,
    },
    {
        text: 'Accounts Dashboard',
        value: DashboardScreenType.Accounts,
        Icon: <Work fontSize='small' />,
    },
    {
        text: 'Operator Dashboard',
        value: DashboardScreenType.Operator,
        Icon: <Computer fontSize='small' />,
    },
    {
        text: 'Origination Dashboard',
        value: DashboardScreenType.Origination,
        Icon: <SyncAlt fontSize='small' />,
    },
    {
        text: 'Verification Dashboard',
        value: DashboardScreenType.Verification,
        Icon: <VerifiedUser fontSize='small' />,
    },
    {
        text: 'Monitor Dashboard',
        value: DashboardScreenType.Monitor,
        Icon: <Monitor fontSize='small' />,
    },
    {
        text: 'Collections Dashboard',
        value: DashboardScreenType.Collections,
        Icon: <Collections fontSize='small' />,
    },
    {
        text: 'Compliance Dashboard',
        value: DashboardScreenType.Compliance,
        Icon: <MenuBook fontSize='small' />,
    },
    {
        text: 'Onboarding Dashboard2',
        value: DashboardScreenType.Onboarding2,
        Icon: <NoteAlt fontSize='small' />,
    },
    {
        text: 'System Health',
        value: DashboardScreenType.System,
        Icon: <Construction fontSize='small' />,
    },
    {
        text: 'Configuration',
        value: DashboardScreenType.Configuration,
        Icon: <Settings fontSize='small' />,
    },
];

export const HeaderViewDropdown = () => {
    const router = useRouter();
    const {routes, activeRoute, setActiveRoute} = useSecured();

    const onChangeRoute = (newRoute: string) => {
        setActiveRoute(newRoute);
        router.push(`/secured/${newRoute}`);
    };

    const handleChange = (event: SelectChangeEvent) => {
        const newRoute = event.target.value as string;

        onChangeRoute(newRoute);
    };

    const renderMenuValue = (path: string) => {
        const menuItem = VALUES.find((item) => item.value === path);

        return menuItem ? (
            <>
                <ListItemIcon sx={{color: activeRoute === path ? 'var(--color-primary)' : 'inherit'}}>
                    {menuItem.Icon}
                </ListItemIcon>{' '}
                {menuItem.text}
            </>
        ) : null;
    };

    const activeValue: ValueModel | undefined = useMemo(() => {
        return VALUES.find((el) => el.value === activeRoute);
    }, [activeRoute]);

    return (
        <div className='kas-header-view-dropdown'>
            <ThemeProvider theme={theme}>
                {routes.length > 3 ? (
                    <KasSelect
                        displayEmpty
                        value={activeRoute}
                        onChange={handleChange}
                        renderValue={() => (
                            <div className='kas-header-view-dropdown__value'>
                                {activeValue ? (
                                    <>
                                        {activeValue?.Icon}
                                        {activeValue?.text}
                                    </>
                                ) : (
                                    'Select menu'
                                )}
                            </div>
                        )}>
                        {routes.map((value) => (
                            <MenuItem key={value} value={value}>
                                {renderMenuValue(value)}
                            </MenuItem>
                        ))}
                    </KasSelect>
                ) : (
                    <Stack direction='row'>
                        {routes.map((value) => (
                            <MenuItem
                                key={value}
                                value={value}
                                selected={activeRoute === value}
                                onClick={() => onChangeRoute(value)}>
                                {renderMenuValue(value)}
                            </MenuItem>
                        ))}
                    </Stack>
                )}
            </ThemeProvider>
        </div>
    );
};
